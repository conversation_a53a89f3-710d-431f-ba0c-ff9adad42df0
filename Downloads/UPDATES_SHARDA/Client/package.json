{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@reduxjs/toolkit": "^2.5.0", "axios": "^1.7.9", "fortawesome": "^0.0.1-security", "lucide-react": "^0.479.0", "phosphor-react": "^1.4.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.1.3", "react-toastify": "^11.0.5", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@heroicons/react": "^2.2.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "daisyui": "^5.0.35", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "framer-motion": "^12.10.5", "globals": "^15.14.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^6.0.5"}}
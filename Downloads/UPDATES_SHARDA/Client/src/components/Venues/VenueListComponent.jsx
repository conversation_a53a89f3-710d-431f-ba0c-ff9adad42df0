import React, { useEffect, useState } from "react";
import axios from "axios";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { FaEdit, FaChevronDown, FaChevronUp, FaCalendarAlt, FaMapMarkerAlt, FaUser } from "react-icons/fa";
import Loader from "../Loader";
import "../../styles/Venues/VenueListComponent.css";

const VenueListComponent = () => {
  const [venues, setVenues] = useState([]);
  const [loading, setLoading] = useState(true);
  const [expandedVenue, setExpandedVenue] = useState(null);
  const { user } = useSelector((state) => state.user);
  const navigate = useNavigate();

  useEffect(() => {
    if (user && user.id) {
      const fetchVenues = async () => {
        try {
          const response = await axios.get(`/api/v1/venues/${user.id}`);
          
          // Sort venues by next nearest deadline
          const sortedVenues = response.data.venues.sort((a, b) => {
            const aDeadline = getNextDeadlineDate(a);
            const bDeadline = getNextDeadlineDate(b);
            
            if (!aDeadline && !bDeadline) return 0;
            if (!aDeadline) return 1;
            if (!bDeadline) return -1;
            
            return aDeadline - bDeadline;
          });
          
          setVenues(sortedVenues);
        } catch (error) {
          console.error("Error fetching venues:", error);
        } finally {
          setLoading(false);
        }
      };
      fetchVenues();
    } else {
      setLoading(false);
    }
  }, [user]);

  const getNextDeadlineDate = (venue) => {
    const dateFields = [
      "abstract_submission",
      "paper_submission",
      "author_response",
      "meta_review",
      "notification",
      "commitment",
      "main_conference_start",
      "main_conference_end",
    ];

    const upcomingDates = dateFields
      .map((field) => (venue[field] ? new Date(venue[field]) : null))
      .filter((date) => date && date > new Date())
      .sort((a, b) => a - b);

    return upcomingDates.length > 0 ? upcomingDates[0] : null;
  };

  const convertToIST = (dateStr, timezone) => {
    if (!dateStr) return null;
    
    try {
      const date = new Date(dateStr);
      
      if (timezone) {
        // Parse timezone offset (e.g., "UTC+5:30", "UTC-12:00")
        const match = timezone.match(/UTC([+-])(\d{1,2}):?(\d{2})?/i);
        if (match) {
          const sign = match[1] === '+' ? 1 : -1;
          const hours = parseInt(match[2], 10);
          const minutes = parseInt(match[3] || '0', 10);
          const timezoneOffsetMinutes = sign * (hours * 60 + minutes);
          
          // IST is UTC+5:30 (330 minutes)
          const istOffsetMinutes = 330;
          
          // Calculate the difference and adjust
          const offsetDifference = istOffsetMinutes - timezoneOffsetMinutes;
          const adjustedDate = new Date(date.getTime() + offsetDifference * 60 * 1000);
          
          return adjustedDate;
        }
      }
      
      // If no timezone or invalid format, return original date
      return date;
    } catch (error) {
      console.error('Error converting timezone:', error);
      return new Date(dateStr);
    }
  };

  const formatDisplayDate = (dateStr, timezone) => {
    if (!dateStr) return "N/A";
    
    const adjustedDate = convertToIST(dateStr, timezone);
    
    if (adjustedDate) {
      return adjustedDate.toLocaleDateString('en-IN', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
        timeZone: 'Asia/Kolkata'
      }) + ' (IST)';
    }
    
    return new Date(dateStr).toLocaleDateString();
  };

  const getNextDeadline = (venue) => {
    const nextDate = getNextDeadlineDate(venue);
    if (!nextDate) return "No upcoming deadlines";
    
    // Convert to IST for display
    return formatDisplayDate(nextDate.toISOString(), venue.time_zone);
  };

  const getDeadlineStatus = (dateString) => {
    if (dateString === "No upcoming deadlines") return "neutral";
    const deadline = new Date(dateString);
    const today = new Date();
    const diffDays = Math.ceil((deadline - today) / (1000 * 60 * 60 * 24));

    if (diffDays <= 7) return "urgent";
    if (diffDays <= 30) return "warning";
    return "normal";
  };

  if (loading) return <Loader />;

  return (
    <div className="compact-venue-list-container">
      <div className="compact-venue-list-header">
        <button
          className="compact-manage-venue-btn"
          onClick={() => navigate("/edit-venue")}
          aria-label="Manage venues"
        >
          <FaEdit className="compact-edit-icon" />
        </button>
        <h3 className="compact-venue-list-title">Venues</h3>
      </div>

      {venues.length === 0 ? (
        <div className="compact-no-venues-message">
          <button
            className="compact-add-venue-btn"
            onClick={() => navigate("/edit-venue")}
          >
            + Add Venue
          </button>
        </div>
      ) : (
        <div className="compact-venue-table-container">
          {venues.map((venue) => (
            <div key={venue._id} className="compact-venue-card">
              <div className="compact-venue-card-header">
                <div className="compact-venue-info">
                  <div className="compact-venue-name">
                    <FaCalendarAlt className="compact-venue-icon" />
                    <span>{venue.venue}</span>
                    {venue.year && (
                    // <div className="compact-venue-year">
                      <span> - {venue.year}</span>
                    // </div>
                  )}
                  </div>
                  
                  <div className="compact-venue-deadline">
                    <span className={`compact-deadline ${getDeadlineStatus(getNextDeadline(venue))}`}>
                      {getNextDeadline(venue)}
                    </span>
                  </div>
                </div>
                <button
                  onClick={() => setExpandedVenue(expandedVenue === venue._id ? null : venue._id)}
                  className="compact-expand-collapse-btn"
                  aria-label={expandedVenue === venue._id ? "Collapse details" : "Expand details"}
                >
                  {expandedVenue === venue._id ? <FaChevronUp /> : <FaChevronDown />}
                </button>
              </div>

              {expandedVenue === venue._id && (
                <div className="compact-venue-details">
                  <div className="compact-detail-item">
                    <span className="compact-detail-label">Location:</span>
                    <span className="compact-detail-value">
                      {venue.location || "N/A"}
                    </span>
                  </div>
                  <div className="compact-detail-item">
                    <span className="compact-detail-label">Website:</span>
                    <span className="compact-detail-value">
                      {venue.url ? (
                        <a
                          href={venue.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="compact-url-link"
                        >
                          {venue.url.replace(/^https?:\/\//, '').split('/')[0]}
                        </a>
                      ) : "N/A"}
                    </span>
                  </div>
                  <div className="compact-detail-item">
                    <span className="compact-detail-label">Time Zone:</span>
                    <span className="compact-detail-value">
                      {venue.time_zone || "Not specified"}
                    </span>
                  </div>
                  <div className="compact-detail-item">
                    <span className="compact-detail-label">Abstract Deadline:</span>
                    <span className="compact-detail-value">
                      {venue.abstract_submission ? formatDisplayDate(venue.abstract_submission, venue.time_zone) : "N/A"}
                    </span>
                  </div>
                  <div className="compact-detail-item">
                    <span className="compact-detail-label">Paper Deadline:</span>
                    <span className="compact-detail-value">
                      {venue.paper_submission ? formatDisplayDate(venue.paper_submission, venue.time_zone) : "N/A"}
                    </span>
                  </div>
                  <div className="compact-detail-item">
                    <span className="compact-detail-label">Author Response:</span>
                    <span className="compact-detail-value">
                      {venue.author_response ? formatDisplayDate(venue.author_response, venue.time_zone) : "N/A"}
                    </span>
                  </div>
                  <div className="compact-detail-item">
                    <span className="compact-detail-label">Meta Review:</span>
                    <span className="compact-detail-value">
                      {venue.meta_review ? formatDisplayDate(venue.meta_review, venue.time_zone) : "N/A"}
                    </span>
                  </div>
                  <div className="compact-detail-item">
                    <span className="compact-detail-label">Notification:</span>
                    <span className="compact-detail-value">
                      {venue.notification ? formatDisplayDate(venue.notification, venue.time_zone) : "N/A"}
                    </span>
                  </div>
                  <div className="compact-detail-item">
                    <span className="compact-detail-label">Commitment:</span>
                    <span className="compact-detail-value">
                      {venue.commitment ? formatDisplayDate(venue.commitment, venue.time_zone) : "N/A"}
                    </span>
                  </div>
                  <div className="compact-detail-item">
                    <span className="compact-detail-label">Conference Start:</span>
                    <span className="compact-detail-value">
                      {venue.main_conference_start ? formatDisplayDate(venue.main_conference_start, venue.time_zone) : "N/A"}
                    </span>
                  </div>
                  <div className="compact-detail-item">
                    <span className="compact-detail-label">Conference End:</span>
                    <span className="compact-detail-value">
                      {venue.main_conference_end ? formatDisplayDate(venue.main_conference_end, venue.time_zone) : "N/A"}
                    </span>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default VenueListComponent;
/* Variables */
:root {
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --error-color: #ef4444;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --text-primary: #1f2937;
  --text-secondary: #4b5563;
  --text-tertiary: #6b7280;
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --border-color: #e5e7eb;
  --shadow-sm: 0 1.0px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 0.286rem 0.429rem -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 0.71rem 1.07rem -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark theme variables */
[data-theme="dark"] {
  --primary-color: #60a5fa;
  --primary-hover: #3b82f6;
  --text-primary: #f3f4f6;
  --text-secondary: #e5e7eb;
  --text-tertiary: #d1d5db;
  --bg-primary: #1f2937;
  --bg-secondary: #374151;
  --bg-tertiary: #4b5563;
  --border-color: #4b5563;
}

/* Dark Theme Styles */
[data-theme="dark"] .submissions-container {
  background-color: #2e3851;
  box-shadow: 0 2.0px 10px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .submissions-header h2 {
  color: #e0e3e7;
}

[data-theme="dark"] .search-box input {
  background-color: #2a3142;
  border-color: #3a4255;
  color: #e0e3e7;
}

[data-theme="dark"] .search-box input:focus {
  border-color: #4d9fec;
  box-shadow: 0 0 0 2.0px rgba(77, 159, 236, 0.3);
}

[data-theme="dark"] .search-box .search-icon {
  color: #a1a8b5;
}

[data-theme="dark"] .filter-btn {
  background-color: #2a3142;
  border-color: #3a4255;
  color: #e0e3e7;
}

[data-theme="dark"] .filter-btn:hover {
  background-color: #3a4255;
}

[data-theme="dark"] .filter-btn.active {
  background-color: #4d9fec;
  color: #1a1f2c;
  border-color: #4d9fec;
}

[data-theme="dark"] .filter-panel {
  background-color: #2a3142;
  border-color: #3a4255;
}

[data-theme="dark"] .filter-group input {
  background-color: #1a1f2c;
  border-color: #3a4255;
  color: #e0e3e7;
}

[data-theme="dark"] .filter-group input:focus {
  border-color: #4d9fec;
}

[data-theme="dark"] .filter-group label {
  color: #a1a8b5;
}

[data-theme="dark"] .reset-filters {
  background-color: #2a3142;
  border-color: #3a4255;
  color: #ff6b6b;
}

[data-theme="dark"] .reset-filters:hover {
  background-color: #3a4255;
}
[data-theme="dark"] .submissions-table {
  background-color: #2a314290;
}

[data-theme="dark"] .submissions-table th {
  background-color: #2a3142;
  color: #e0e3e7;
  border-bottom-color: #3a4255;
}

[data-theme="dark"] .submissions-table th:hover {
  background-color: #3a4255;
}

[data-theme="dark"] .submissions-table td {
  color: #e0e3e7;
  border-bottom-color: #3a4255;
}

[data-theme="dark"] .submissions-table tr:hover td {
  background-color: #2a3142;
}

[data-theme="dark"] .sort-icon {
  color: #a1a8b5;
}

[data-theme="dark"] .url-link {
  color: #4d9fec;
}

[data-theme="dark"] .remarks-tooltip::after {
  background-color: #3a4255;
  color: #e0e3e7;
}

[data-theme="dark"] .no-submissions {
  background-color: #2a3142;
  color: #a1a8b5;
}

[data-theme="dark"] .submissions-error {
  background-color: rgba(255, 107, 107, 0.1);
  border-left-color: #ff6b6b;
  color: #ff6b6b;
}

/* Dark theme animations */
@keyframes submissionsFadeInDark {
  from {
    opacity: 0;
    background-color: transparent;
  }
  to {
    opacity: 1;
    background-color: #1a1f2c;
  }
}

[data-theme="dark"] .submissions-container {
  animation: submissionsFadeInDark 0.3s ease-out forwards;
}

/* Enhanced Base Styles */
.submissions-container {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  width: 100%;
  padding: 1.60rem;
  background-color: var(--bg-primary);
  border-radius: 1.14rem;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-normal);
  border: 1.0px solid var(--border-color);
  backdrop-filter: blur(0.71rem);
}

.submissions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.submissions-header h2 {
  color: var(--text-primary);
  margin: 0;
  font-size: 1.20rem;
  font-weight: 700;
  letter-spacing: -0.025em;
  line-height: 1.00.2;
  background: linear-gradient(to right, var(--primary-color), var(--primary-hover));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: titleFade 0.5s ease-out;
}

.submissions-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* Enhanced Search Box */
.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box .search-icon {
  position: absolute;
  left: 0.86rem;
  color: var(--text-tertiary);
  font-size: 0.75rem;
  transition: var(--transition-fast);
}

.search-box input {
  padding: 0.875rem 1rem 0.875rem 2.5rem;
  border: 2.0px solid var(--border-color);
  border-radius: 0.86rem;
  min-width: 16.1rem;
  font-size: 0.80rem;
  transition: var(--transition-normal);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.286rem rgba(59, 130, 246, 0.1);
  background-color: var(--bg-primary);
}

.search-box input:focus + .search-icon {
  color: var(--primary-color);
}

/* Enhanced Filter Button */
.filter-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  background-color: var(--bg-tertiary);
  border: 2.0px solid var(--border-color);
  border-radius: 0.86rem;
  cursor: pointer;
  font-size: 0.80rem;
  font-weight: 600;
  color: var(--text-primary);
  transition: var(--transition-normal);
}

.filter-btn:hover {
  background-color: var(--bg-secondary);
  transform: translateY(-1.0px);
  box-shadow: var(--shadow-md);
}

.filter-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Enhanced Filter Panel */
.filter-panel {
  background-color: var(--bg-secondary);
  border-radius: 1.14rem;
  padding: 1.75rem;
  margin-bottom: 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(20.0rem, 1fr));
  gap: 1.75rem;
  border: 2.0px solid var(--border-color);
  animation: slideDown 0.3s var(--transition-normal);
  box-shadow: var(--shadow-md);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.filter-group label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 600;
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.filter-group input {
  padding: 0.875rem;
  border: 2.0px solid var(--border-color);
  border-radius: 0.86rem;
  font-size: 0.80rem;
  background-color: var(--bg-primary);
  transition: var(--transition-normal);
  color: var(--text-primary);
}

.filter-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.286rem rgba(59, 130, 246, 0.1);
}

/* Enhanced Table Styles */
.submissions-table-container {
  overflow-x: auto;
  border-radius: 1.14rem;
  box-shadow: var(--shadow-md);
  border: 2.0px solid var(--border-color);
  background-color: var(--bg-primary);
}

.submissions-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background-color: var(--bg-primary);
}

.submissions-table th {
  background-color: var(--bg-secondary);
  padding: 1.25rem 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 2.0px solid var(--border-color);
  white-space: nowrap;
  cursor: pointer;
  transition: var(--transition-normal);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-size: 0.75rem;
}

.submissions-table td {
  padding: 1.25rem 1rem;
  color: var(--text-primary);
  border-bottom: 1.0px solid var(--border-color);
  transition: var(--transition-normal);
  line-height: 1.00.5;
}

.submissions-table tr:hover td {
  background-color: var(--bg-secondary);
}

/* Enhanced URL Link */
.url-link {
  color: var(--primary-color);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 0.57rem;
  transition: var(--transition-normal);
  background-color: transparent;
}

.url-link:hover {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--primary-hover);
  transform: translateY(-1.0px);
}

/* Enhanced Tooltip */
.remarks-tooltip {
  position: relative;
  color: var(--error-color);
  margin-left: 0.5rem;
  cursor: help;
  display: inline-block;
  transition: var(--transition-normal);
}

.remarks-tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(0.357rem);
  padding: 0.75rem 1rem;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.75rem;
  border-radius: 0.57rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-normal);
  z-index: 10;
  box-shadow: var(--shadow-lg);
  border: 1.0px solid var(--border-color);
  pointer-events: none;
}

.remarks-tooltip:hover::after {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

/* Enhanced Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 714.2rem;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
}

.status-indicator.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.reset-filters{
  margin-top: 2.1rem;
  max-width: 5.4rem;
  max-height: 2.1rem;
}

.status-indicator.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.status-indicator.error {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

/* Enhanced Animations */
@keyframes titleFade {
  from {
    opacity: 0;
    transform: translateY(-0.71rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-1.43rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Responsive Styles */
@media (max-width: 41.1rem) {
  .submissions-container {
    padding: 1.5rem;
    border-radius: 0.86rem;
  }

  .submissions-header h2 {
    font-size: 1.12rem;
  }

  .search-box input {
    min-width: unset;
    width: 100%;
  }

  .filter-panel {
    padding: 1.25rem;
    gap: 1.25rem;
  }
}

@media (max-width: 25.7rem) {
  .submissions-container {
    padding: 1rem;
    border-radius: 0.57rem;
  }

  .submissions-header h2 {
    font-size: 0.95rem;
  }

  .submissions-table th,
  .submissions-table td {
    padding: 1rem 0.75rem;
    font-size: 0.75rem;
  }

  .filter-btn {
    padding: 0.75rem 1rem;
  }
}

/* Print Styles */
@media print {
  .submissions-container {
    box-shadow: none;
    padding: 0;
  }

  .submissions-controls,
  .filter-panel,
  .url-link {
    display: none;
  }

  .submissions-table th,
  .submissions-table td {
    padding: 0.5rem;
    border-color: #000;
  }
}
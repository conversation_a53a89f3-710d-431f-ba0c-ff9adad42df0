/* Dark Theme Styles */
[data-theme="dark"] .equipment-container {
  background-color: #1a1f2c;
  color: #e0e0e0;
  box-shadow: 0 2.0px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .equipment-header {
  border-bottom-color: #2e2e2e;
}

[data-theme="dark"] .equipment-title {
  color: #ffffff;
}

[data-theme="dark"] .equipment-title-icon {
  color: #1e88e5;
}

[data-theme="dark"] .equipment-stat-card {
  background: #222836;
  border-color: #2e2e2e;
  box-shadow: 0 0.214rem 0.429rem rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .equipment-stat-card:hover {
  border-color: rgba(30, 136, 229, 0.3);
  box-shadow: 0 0.429rem 0.86rem rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .equipment-stat-icon-wrapper {
  background: #1e88e5;
}

[data-theme="dark"] .equipment-stat-number {
  color: #ffffff;
}

[data-theme="dark"] .equipment-stat-label {
  color: #9e9e9e;
}

[data-theme="dark"] .equipment-view-toggle-group {
  background: #222836;
  border-color: #2e2e2e;
}

[data-theme="dark"] .equipment-view-toggle {
  color: #9e9e9e;
}

[data-theme="dark"] .equipment-view-toggle:hover:not(.equipment-active) {
  background: #222836;
}

[data-theme="dark"] .equipment-view-toggle.equipment-active {
  background: #1e88e5;
  color: white;
}

[data-theme="dark"] .equipment-manage-button {
  background: #43a047;
  box-shadow: 0 2.0px 4px rgba(67, 160, 71, 0.3);
}

[data-theme="dark"] .equipment-manage-button:hover {
  background: #388e3c;
}

[data-theme="dark"] .equipment-search-input {
  background: #222836;
  border-color: #2e2e2e;
  color: #e0e0e0;
}

[data-theme="dark"] .equipment-search-input:focus {
  border-color: #1e88e5;
  box-shadow: 0 0 0 0.214rem rgba(30, 136, 229, 0.3);
}

[data-theme="dark"] .equipment-search-input::placeholder {
  color: #757575;
}

[data-theme="dark"] .equipment-search-icon {
  color: #757575;
}

[data-theme="dark"] .equipment-table-container {
  background: #1e1e1e;
  border-color: #2e2e2e;
  box-shadow: 0 2.0px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .equipment-table {
  background: #222836;
}

[data-theme="dark"] .equipment-table th {
  background: #25304b;
  color: #9e9e9e;
  border-bottom-color: #2e2e2e;
}

[data-theme="dark"] .equipment-table td {
  border-bottom-color: #2e2e2e;
  color: #e0e0e0;
}

[data-theme="dark"] .equipment-table-row:hover {
  background: #2e2e2e;
}

[data-theme="dark"] .equipment-table-name {
  color: #ffffff;
}

[data-theme="dark"] .equipment-table-icon {
  color: #1e88e5;
}

[data-theme="dark"] .equipment-table-price {
  color: #ffffff;
}

[data-theme="dark"] .equipment-cards-grid {
  background-color: transparent;
}

[data-theme="dark"] .equipment-card {
  background: #222836;
  border-color: #2e2e2e;
  box-shadow: 0 0.214rem 0.429rem rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .equipment-card:hover {
  border-color: rgba(30, 136, 229, 0.3);
  box-shadow: 0 0.57rem 1.14rem rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .equipment-card-header {
  background: #25304b;
  border-bottom-color: #3e3e3e;
}

[data-theme="dark"] .equipment-card-title {
  color: #ffffff;
}

[data-theme="dark"] .equipment-card-price {
  color: #ffffff;
}

[data-theme="dark"] .equipment-price-icon {
  color: #1e88e5;
}

[data-theme="dark"] .equipment-card-icon {
  color: #1e88e5;
}

[data-theme="dark"] .equipment-card-label {
  color: #9e9e9e;
}

[data-theme="dark"] .equipment-card-value {
  color: #e0e0e0;
}

[data-theme="dark"] .equipment-empty-state {
  background: #1e1e1e;
  border-color: #2e2e2e;
  box-shadow: 0 0.214rem 0.429rem rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .equipment-empty-state h3 {
  color: #ffffff;
}

[data-theme="dark"] .equipment-empty-state p {
  color: #9e9e9e;
}

[data-theme="dark"] .equipment-empty-icon {
  color: #3e3e3e;
}

[data-theme="dark"] .equipment-add-button {
  background: #1e88e5;
  box-shadow: 0 2.0px 4px rgba(30, 136, 229, 0.3);
}

[data-theme="dark"] .equipment-add-button:hover {
  background: #1565c0;
}

[data-theme="dark"] .equipment-message {
  background: #1e1e1e;
  color: #b0b0b0;
  box-shadow: 0 2.0px 4px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .equipment-error {
  color: #ff5252;
  background: rgba(239, 83, 80, 0.1);
  border-color: rgba(239, 83, 80, 0.2);
}

/* Scrollbar styling for dark theme */
[data-theme="dark"] .equipment-table-container::-webkit-scrollbar-track {
  background: #1e1e1e;
}

[data-theme="dark"] .equipment-table-container::-webkit-scrollbar-thumb {
  background: #3e3e3e;
}
/* Equipment Container */
.equipment-container {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #333;
  padding: 1.21rem;
  max-width: 96.4rem;
  margin: 0 auto;
  background-color: #f8f9fa;
  border-radius: 0.86rem;
  box-shadow: 0 2.0px 8px rgba(0, 0, 0, 0.04);
}

/* Header Styles */
.equipment-header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.46rem;
  gap: 1.21rem;
  border-bottom: 1.0px solid rgba(0,0,0,0.05);
  padding-bottom: 1.14rem;
}

.equipment-header-left {
  flex: 1;
  min-width: 16.1rem;
}

.equipment-title {
  display: flex;
  align-items: center;
  font-size: 1.20rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 1.14rem;
}

.equipment-title-icon {
  margin-right: 0.86rem;
  color: #3498db;
}

/* Stats Container */
.equipment-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 1.00rem;
  margin-top: 1.14rem;
}

.equipment-stat-card {
  min-width: 8.0rem;
  background: white;
  border-radius: 0.71rem;
  padding: 1.07rem;
  box-shadow: 0 0.214rem 0.429rem rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.25s ease;
  border: 1.0px solid rgba(0,0,0,0.03);
}

.equipment-stat-card:hover {
  transform: translateY(-0.214rem);
  box-shadow: 0 0.429rem 0.86rem rgba(0, 0, 0, 0.08);
  border-color: rgba(52, 152, 219, 0.2);
}

.equipment-stat-icon-wrapper {
  width: 2.2rem;
  height: 2.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3498db;
  color: white;
  border-radius: 50%;
  margin-right: 1.07rem;
}

.equipment-stat-content {
  display: flex;
  flex-direction: column;
}

.equipment-stat-number {
  font-size: 1.12rem;
  font-weight: bold;
  line-height: 1.00.2;
  color: #2c3e50;
}

.equipment-stat-label {
  font-size: 0.75rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 2.0px;
}

/* Actions */
.equipment-actions {
  display: flex;
  gap: 1.00rem;
  align-items: center;
}

.equipment-view-toggle-group {
  display: flex;
  background: #f0f2f5;
  border-radius: 0.57rem;
  overflow: hidden;
  border: 1.0px solid rgba(0,0,0,0.05);
}

.equipment-view-toggle {
  padding: 0.71rem 1.07rem;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.57rem;
  color: #7f8c8d;
  transition: all 0.2s ease;
}

.equipment-view-toggle:hover:not(.equipment-active) {
  background: rgba(0,0,0,0.03);
}

.equipment-view-toggle.equipment-active {
  background: #3498db;
  color: white;
}

.equipment-manage-button {
  padding: 0.71rem 1.29rem;
  background: #2ecc71;
  color: white;
  border: none;
  border-radius: 0.57rem;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.57rem;
  transition: all 0.2s ease;
  box-shadow: 0 2.0px 4px rgba(46, 204, 113, 0.2);
}

.equipment-manage-button:hover {
  background: #27ae60;
  transform: translateY(-1.0px);
  box-shadow: 0 0.286rem 0.57rem rgba(46, 204, 113, 0.25);
}

.equipment-manage-button:active {
  transform: translateY(0);
}

/* Controls */
.equipment-controls {
  margin-bottom: 1.46rem;
}

.equipment-search-container {
  position: relative;
  max-width: 32.1rem;
  width: 100%;
}

.equipment-search-icon {
  position: absolute;
  left: 1.07rem;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
}

.equipment-search-input {
  width: 100%;
  padding: 0.86rem 1.07rem 12px 45px;
  border: 1.0px solid #ddd;
  border-radius: 0.57rem;
  font-size: 0.80rem;
  transition: all 0.2s ease;
  background-color: white;
}

.equipment-search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 0.214rem rgba(52, 152, 219, 0.15);
}

.equipment-search-input::placeholder {
  color: #aaa;
}

/* Table View */
.equipment-table-container {
  overflow-x: auto;
  margin-top: 1.14rem;
  border-radius: 0.71rem;
  box-shadow: 0 2.0px 8px rgba(0, 0, 0, 0.06);
  background-color: white;
  border: 1.0px solid rgba(0,0,0,0.04);
}

.equipment-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.equipment-table th {
  background: #f5f7fa;
  color: #4b5563;
  font-weight: 600;
  text-align: left;
  padding: 1.00rem 1.14rem;
  border-bottom: 1.0px solid #e5e7eb;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.6px;
  white-space: nowrap;
}

.equipment-table td {
  padding: 0.86rem 1.14rem;
  border-bottom: 1.0px solid #e5e7eb;
  vertical-align: middle;
  font-size: 0.75rem;
}

.equipment-table tr:last-child td {
  border-bottom: none;
}

.equipment-table-row {
  transition: background-color 0.15s ease;
}

.equipment-table-row:hover {
  background: #f8fafc;
}

.equipment-table-name {
  font-weight: 500;
  color: #2c3e50;
}

.equipment-table-icon-content {
  display: flex;
  align-items: center;
  gap: 0.57rem;
}

.equipment-table-icon {
  color: #3498db;
  flex-shrink: 0;
}

.equipment-table-price {
  display: flex;
  align-items: center;
  gap: 0.286rem;
  font-weight: 500;
  color: #2c3e50;
}

/* Cards View */
.equipment-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(22.9rem, 1fr));
  gap: 1.21rem;
  margin-top: 1.14rem;
}

.equipment-card {
  background: white;
  border-radius: 0.71rem;
  overflow: hidden;
  box-shadow: 0 0.214rem 0.429rem rgba(0, 0, 0, 0.08);
  transition: all 0.25s ease;
  border: 1.0px solid rgba(0,0,0,0.04);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.equipment-card:hover {
  transform: translateY(-0.357rem);
  box-shadow: 0 0.57rem 1.14rem rgba(0, 0, 0, 0.1);
  border-color: rgba(52, 152, 219, 0.15);
}

.equipment-card-header {
  padding: 1.14rem 1.21rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
  border-bottom: 1.0px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.equipment-card-title {
  margin: 0;
  font-size: 0.90rem;
  color: #2c3e50;
  font-weight: 600;
  word-break: break-word;
}

.equipment-card-price {
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.286rem;
  white-space: nowrap;
  padding-left: 0.57rem;
}

.equipment-price-icon {
  color: #3498db;
}

.equipment-card-body {
  padding: 1.14rem 1.21rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.86rem;
}

.equipment-card-detail {
  display: flex;
  align-items: flex-start;
  gap: 0.86rem;
}

.equipment-card-icon {
  color: #3498db;
  margin-top: 2.0px;
  flex-shrink: 0;
}

.equipment-card-detail-content {
  flex: 1;
}

.equipment-card-label {
  display: block;
  font-size: 0.75rem;
  color: #7f8c8d;
  margin-bottom: 2.0px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.equipment-card-value {
  font-size: 0.75rem;
  color: #2c3e50;
  word-break: break-word;
}

/* Empty State */
.equipment-empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 15.0rem;
  text-align: center;
  background: white;
  border-radius: 0.71rem;
  padding: 1.94rem;
  margin: 1.46rem 0;
  box-shadow: 0 0.214rem 0.429rem rgba(0, 0, 0, 0.06);
  border: 1.0px solid rgba(0,0,0,0.04);
}

.equipment-empty-content {
  max-width: 21.4rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.equipment-empty-icon {
  color: #bdc3c7;
  margin-bottom: 1.14rem;
}

.equipment-empty-state h3 {
  color: #4b5563;
  margin-bottom: 0.57rem;
  font-size: 0.88rem;
}

.equipment-empty-state p {
  color: #7f8c8d;
  margin-bottom: 1.21rem;
  font-size: 0.80rem;
}

.equipment-add-button {
  padding: 0.71rem 1.21rem;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 0.57rem;
  cursor: pointer;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.57rem;
  transition: all 0.2s ease;
  box-shadow: 0 2.0px 4px rgba(52, 152, 219, 0.2);
}

.equipment-add-button:hover {
  background: #2980b9;
  transform: translateY(-2.0px);
  box-shadow: 0 0.286rem 0.57rem rgba(52, 152, 219, 0.25);
}

.equipment-add-button:active {
  transform: translateY(0);
}

/* Messages */
.equipment-message {
  padding: 1.21rem;
  text-align: center;
  color: #7f8c8d;
  background-color: white;
  border-radius: 0.57rem;
  box-shadow: 0 2.0px 4px rgba(0, 0, 0, 0.05);
  margin: 1.14rem 0;
}

.equipment-error {
  color: #e74c3c;
  background: #fef2f2;
  border: 1.0px solid #fee2e2;
  border-radius: 0.57rem;
}

/* Responsive Adjustments */
@media (max-width: 64.3rem) {
  .equipment-cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(20.0rem, 1fr));
  }
  
  .equipment-container {
    padding: 1.14rem;
  }
}

@media (max-width: 53.1rem) {
  .equipment-header {
    gap: 1.14rem;
  }
  
  .equipment-title {
    font-size: 1.00rem;
  }
}

@media (max-width: 41.1rem) {
  .equipment-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .equipment-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .equipment-stats {
    gap: 0.71rem;
    width: 100%;
  }
  
  .equipment-stat-card {
    min-width: calc(50% - 0.71rem);
  }
  
  .equipment-search-container {
    max-width: 100%;
  }
  
  .equipment-empty-state {
    padding: 1.46rem;
  }
}

@media (max-width: 30.9rem) {
  .equipment-stat-card {
    min-width: 100%;
  }
  
  .equipment-cards-grid {
    grid-template-columns: 1fr;
  }
  
  .equipment-table th, .equipment-table td {
    padding: 0.71rem;
    font-size: 0.75rem;
  }
  
  .equipment-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .equipment-view-toggle-group {
    width: 100%;
  }
  
  .equipment-view-toggle {
    flex: 1;
    justify-content: center;
  }
  
  .equipment-manage-button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 21.4rem) {
  .equipment-container {
    padding: 0.86rem;
  }
  
  .equipment-title {
    font-size: 0.88rem;
  }
  
  .equipment-empty-state h3 {
    font-size: 0.90rem;
  }
}
.rema-loader-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, #1a1a2e 0%, #16213e 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  overflow: hidden;
  opacity: 1;
  transition: opacity 0.8s ease-out;
}

.rema-loader-fade-out {
  opacity: 0;
  pointer-events: none;
}

.rema-loader-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  position: relative;
  z-index: 2;
}

.rema-loader-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.rema-loader-particle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.8;
  animation: rema-loader-particle-float linear infinite;
  filter: blur(1.0px);
}

@keyframes rema-loader-particle-float {
  0% { transform: translateY(0) rotate(0deg); opacity: 0.8; }
  100% { transform: translateY(-7.1rem) rotate(360deg); opacity: 0; }
}

.rema-loader-logo {
  display: flex;
  font-size: 2.10rem;
  font-weight: 700;
  text-shadow: 0 0 0.71rem rgba(255, 255, 255, 0.3);
}

.rema-loader-letter {
  display: inline-block;
  transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transform-origin: center bottom;
}

.rema-loader-active-letter {
  animation: rema-loader-letter-bounce 0.8s ease infinite alternate;
  text-shadow: 0 0 1.43rem currentColor;
}

.rema-loader-s { color: #4cc9f0; }
.rema-loader-h { color: #f72585; }
.rema-loader-a { color: #b5179e; }
.rema-loader-r { color: #7209b7; }
.rema-loader-d { color: #3a0ca3; }
.rema-loader-a { color: #4361ee; }
.rema-loader-space {
  width: 0.5em;
  display: inline-block;
}
.rema-loader-p { color: #4cc9f0; }
.rema-loader-o { color: #f72585; }
.rema-loader-r { color: #b5179e; }
.rema-loader-t { color: #7209b7; }
.rema-loader-a { color: #3a0ca3; }
.rema-loader-l { color: #4361ee; }

@keyframes rema-loader-letter-bounce {
  0% { transform: translateY(0) scale(1); }
  100% { transform: translateY(-1.43rem) scale(1.2); }
}

.rema-loader-circular-progress {
  position: relative;
  width: 6.4rem;
  height: 6.4rem;
}

.rema-loader-progress-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.rema-loader-progress-circle {
  fill: none;
  stroke-width: 8;
  stroke-linecap: round;
  stroke-dasharray: 283;
  stroke-dashoffset: 283;
  stroke: url(#rema-loader-gradient);
  animation: rema-loader-fill 5s linear forwards;
}

.rema-loader-progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.12rem;
  font-weight: bold;
  color: white;
  animation: rema-loader-count 5s linear forwards;
}

.rema-loader-gradient-text {
  background: linear-gradient(90deg, #4cc9f0, #f72585, #b5179e, #7209b7);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-size: 1.12rem;
  font-weight: 600;
  letter-spacing: 1.0px;
}

@keyframes rema-loader-fill {
  0% { stroke-dashoffset: 283; }
  100% { stroke-dashoffset: 0; }
}

@keyframes rema-loader-count {
  0% { content: "0%"; }
  100% { content: "100%"; }
}

/* SVG gradient definition */
.rema-loader-progress-svg defs linearGradient {
  id: "rema-loader-gradient";
  x1: "0%";
  y1: "0%";
  x2: "100%";
  y2: "100%";
}
.rema-loader-progress-svg defs linearGradient stop {
  offset: "0%";
  stop-color: "#4cc9f0";
}
.rema-loader-progress-svg defs linearGradient stop {
  offset: "100%";
  stop-color: "#7209b7";
}
.landingpage-dashboard {
  text-align: center;
  padding: 0.0px;
  height: 72.0vh;
  overflow: hidden;
}

.landingpage-header h1 {
  font-size: 1.14rem;
  color: #333;
}

.landingpage-content {
  display: flex;
  gap: 1.21rem;
  justify-content: center;
  height: 100%;
}
.notifications-main{
  width: 80%;
}

.venues-compact{
  width: 25%;
}

.dashboard-section-right {
  display: flex;
  flex-direction: column;
  max-height: 100%;
  gap: 5rem;
  scrollbar-width: none;
}

.landingpage-section-content::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.landingpage-section-content{
  max-height: 60%;
  scrollbar-width: none;
}
.currentMonthLeave{
  max-height: 40%;
}

@media (max-width: 75.0rem) {
  .landingpage-content {
    flex-direction: column;
  }
  .notifications-main{
    width: 100%;
  }
  .landingpage-notification {
    width: 100%;
  }
  .venues-compact{
    width: 40%;
  }

  .landingpage-venue {
    width: 100%;
  }
}
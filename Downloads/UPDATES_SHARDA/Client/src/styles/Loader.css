/* Loader.css */

.loader-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%; /* Take up full width of parent */
  min-height: 5.4rem; /* Ensure it's visible even without content */
  /* Add padding or margin as needed, depending on the context where it's used */
  padding: 1.21rem;
}

.spinner {
  border-radius: 50%;
  border-style: solid;
  border-width: 0.286rem; /* Adjust thickness */
  animation: spin 1s linear infinite; /* Animation */
}

.loader-text {
  margin-top: 1.07rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: #3498db; /* Default color, overridden by prop */
}

/* Keyframes animation for the spinner */
@keyframes spin {
  0% {
      transform: rotate(0deg);
  }
  100% {
      transform: rotate(360deg);
  }
}
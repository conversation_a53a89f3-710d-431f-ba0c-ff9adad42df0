.forgot-password-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 1.21rem;
}

.forgot-password-card {
    background: white;
    border-radius: 0.86rem;
    box-shadow: 0 0.71rem 2.1rem rgba(0, 0, 0, 0.1);
    padding: 1.68rem;
    width: 100%;
    max-width: 24.1rem;
    text-align: center;
}

.forgot-password-header {
    margin-bottom: 1.82rem;
}

.password-icon {
    width: 4.3rem;
    height: 4.3rem;
    margin: 0 auto 1.21rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f4ff;
    border-radius: 50%;
}

.password-icon svg {
    font-size: 1.50rem;
    color: #4a6baf;
}

.forgot-password-header h2 {
    color: #2c3e50;
    margin-bottom: 0.71rem;
    font-size: 1.20rem;
}

.forgot-password-header p {
    color: #7f8c8d;
    font-size: 0.75rem;
    margin: 0;
}

.forgot-password-form {
    display: flex;
    flex-direction: column;
    gap: 1.52rem;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 0.57rem;
    text-align: left;
}

.input-group label {
    font-size: 0.75rem;
    color: #555;
    font-weight: 500;
}

.input-wrapper {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 1.07rem;
    top: 50%;
    transform: translateY(-50%);
    color: #95a5a6;
}

.input-wrapper input {
    width: 100%;
    padding: 1.00rem 1.14rem 14px 45px;
    border: 1.0px solid #ddd;
    border-radius: 0.57rem;
    font-size: 0.75rem;
    transition: all 0.3s ease;
}

.input-wrapper input:focus {
    outline: none;
    border-color: #4a6baf;
    box-shadow: 0 0 0 0.214rem rgba(74, 107, 175, 0.2);
}

.submit-button {
    background-color: #4a6baf;
    color: white;
    border: none;
    padding: 1.00rem;
    border-radius: 0.57rem;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.71rem;
}

.submit-button:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
}

.submit-button:not(:disabled):hover {
    background-color: #3a5a9f;
    transform: translateY(-2.0px);
    box-shadow: 0 0.286rem 0.71rem rgba(74, 107, 175, 0.3);
}

.message {
    padding: 0.86rem 1.14rem;
    border-radius: 0.57rem;
    margin: 1.21rem 0;
    font-size: 0.75rem;
    text-align: center;
}

.message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1.0px solid #c3e6cb;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1.0px solid #f5c6cb;
}

.forgot-password-footer {
    margin-top: 1.21rem;
    color: #7f8c8d;
    font-size: 0.75rem;
}

.forgot-password-footer a {
    color: #4a6baf;
    text-decoration: none;
    font-weight: 500;
}

.forgot-password-footer a:hover {
    text-decoration: underline;
}

@media (max-width: 30.9rem) {
    .forgot-password-card {
        padding: 1.82rem 1.21rem;
    }
    
    .password-icon {
        width: 3.2rem;
        height: 3.2rem;
    }
    
    .password-icon svg {
        font-size: 1.25rem;
    }
    
    .forgot-password-header h2 {
        font-size: 1.12rem;
    }
}
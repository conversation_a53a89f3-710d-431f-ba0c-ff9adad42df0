/* Dark Theme Styles */
[data-theme="dark"] .loginPage-container {
  background: linear-gradient(135deg, #1a1f2c 0%, #0d111a 100%);
}

[data-theme="dark"] .loginPage-card {
  background-color: #2a3142;
  box-shadow: 0 0.71rem 2.1rem rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .loginPage-logo {
  background-color: rgba(77, 159, 236, 0.1);
}

[data-theme="dark"] .loginPage-logo-icon {
  color: #4d9fec;
}

[data-theme="dark"] .loginPage-title {
  color: #e0e3e7;
}

[data-theme="dark"] .loginPage-subtitle {
  color: #a1a8b5;
}

[data-theme="dark"] .loginPage-error {
  background-color: rgba(255, 107, 107, 0.1);
  color: #ff6b6b;
  border-color: rgba(255, 107, 107, 0.3);
}

[data-theme="dark"] .loginPage-input-label {
  color: #e0e3e7;
}

[data-theme="dark"] .loginPage-input {
  background-color: #1a1f2c;
  border-color: #3a4255;
  color: #e0e3e7;
}

[data-theme="dark"] .loginPage-input:focus {
  border-color: #4d9fec;
  box-shadow: 0 0 0 0.214rem rgba(77, 159, 236, 0.3);
}

[data-theme="dark"] .loginPage-input-icon,
[data-theme="dark"] .loginPage-password-toggle {
  color: #7f8c8d;
}

[data-theme="dark"] .loginPage-password-toggle:hover {
  color: #4d9fec;
}

[data-theme="dark"] .loginPage-remember {
  color: #e0e3e7;
}

[data-theme="dark"] .loginPage-checkbox {
  accent-color: #4d9fec;
}

[data-theme="dark"] .loginPage-forgot-password {
  color: #4d9fec;
}

[data-theme="dark"] .loginPage-button {
  background-color: #4d9fec;
  color: #1a1f2c;
}

[data-theme="dark"] .loginPage-button:disabled {
  background-color: #7f8c8d;
  color: #2a3142;
}

[data-theme="dark"] .loginPage-button:not(:disabled):hover {
  background-color: #3a8fd9;
}

/* Placeholder text color */
[data-theme="dark"] .loginPage-input::placeholder {
  color: #7f8c8d;
}

/* Animation for dark theme */
@keyframes loginFadeInDark {
  from {
    opacity: 0;
    background-color: transparent;
  }
  to {
    opacity: 1;
    background-color: #2a3142;
  }
}

[data-theme="dark"] .loginPage-card {
  animation: loginFadeInDark 0.3s ease-out forwards;
}

/* Responsive adjustments for dark theme */
@media (max-width: 30.9rem) {
  [data-theme="dark"] .loginPage-card {
    background-color: #2a3142;
  }
}

.loginPage-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 1.21rem;
}

.loginPage-card {
  background: white;
  border-radius: 0.86rem;
  box-shadow: 0 0.71rem 2.1rem rgba(0, 0, 0, 0.1);
  padding: 1.68rem;
  width: 100%;
  max-width: 24.1rem;
}

.loginPage-header {
  text-align: center;
  margin-bottom: 1.82rem;
}

.loginPage-logo {
  width: 4.3rem;
  height: 4.3rem;
  margin: 0 auto 1.21rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f4ff;
  border-radius: 50%;
}

.loginPage-logo-icon {
  font-size: 1.50rem;
  color: #4a6baf;
}

.loginPage-title {
  color: #2c3e50;
  margin-bottom: 0.71rem;
  font-size: 1.20rem;
}

.loginPage-subtitle {
  color: #7f8c8d;
  font-size: 0.75rem;
  margin: 0;
}

.loginPage-loader {
  display: flex;
  justify-content: center;
  padding: 1.68rem 0;
}

.loginPage-form {
  display: flex;
  flex-direction: column;
  gap: 1.21rem;
}

.loginPage-error {
  padding: 0.86rem 1.14rem;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 0.57rem;
  font-size: 0.75rem;
  text-align: center;
  border: 1.0px solid #f5c6cb;
}

.loginPage-input-group {
  display: flex;
  flex-direction: column;
  gap: 0.57rem;
}

.loginPage-input-label {
  font-size: 0.75rem;
  color: #555;
  font-weight: 500;
}

.loginPage-input-wrapper {
  position: relative;
}

.loginPage-input-icon {
  position: absolute;
  left: 1.07rem;
  top: 50%;
  transform: translateY(-50%);
  color: #95a5a6;
}

.loginPage-input {
  width: 100%;
  padding: 1.00rem 1.14rem 14px 45px;
  border: 1.0px solid #ddd;
  border-radius: 0.57rem;
  font-size: 0.75rem;
  transition: all 0.3s ease;
}

.loginPage-input:focus {
  outline: none;
  border-color: #4a6baf;
  box-shadow: 0 0 0 0.214rem rgba(74, 107, 175, 0.2);
}

.loginPage-password-toggle {
  position: absolute;
  right: 1.07rem;
  top: 50%;
  transform: translateY(-50%);
  color: #95a5a6;
  cursor: pointer;
  transition: color 0.2s ease;
}

.loginPage-password-toggle:hover {
  color: #4a6baf;
}

.loginPage-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.loginPage-remember {
  display: flex;
  align-items: center;
  gap: 0.57rem;
  color: #555;
}

.loginPage-checkbox {
  accent-color: #4a6baf;
}

.loginPage-forgot-password {
  color: #4a6baf;
  text-decoration: none;
  font-weight: 500;
}

.loginPage-forgot-password:hover {
  text-decoration: underline;
}

.loginPage-button {
  background-color: #4a6baf;
  color: white;
  border: none;
  padding: 1.00rem;
  border-radius: 0.57rem;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.71rem;
}

.loginPage-button:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.loginPage-button:not(:disabled):hover {
  background-color: #3a5a9f;
  transform: translateY(-2.0px);
  box-shadow: 0 0.286rem 0.71rem rgba(74, 107, 175, 0.3);
}

.loginPage-button-icon {
  font-size: 0.75em;
}

.loginPage-footer {
  margin-top: 1.21rem;
  text-align: center;
  color: #7f8c8d;
  font-size: 0.75rem;
}

.loginPage-signup-link {
  color: #4a6baf;
  text-decoration: none;
  font-weight: 500;
}

.loginPage-signup-link:hover {
  text-decoration: underline;
}

/* Enhanced Responsive Styles */
@media (max-width: 32.9rem) {
  .loginPage-container {
    padding: 1rem;
  }
  
  .loginPage-card {
    width: 95%;
    max-width: none;
    padding: 1.5rem;
    margin: 1rem auto;
  }
  
  .loginPage-title {
    font-size: 1.20rem;
  }
  
  .loginPage-subtitle {
    font-size: 0.80rem;
  }
  
  .loginPage-input-group {
    margin-bottom: 1rem;
  }
  
  .loginPage-submit-btn, .loginPage-button {
    padding: 0.75rem;
    font-size: 0.90rem;
  }
}

@media (max-width: 30.9rem) {
  .loginPage-card {
      padding: 1.21rem;
      border-radius: 0.5rem;
      width: 90%;
  }
  
  .loginPage-logo {
      width: 3rem;
      height: 3rem;
  }
  
  .loginPage-logo-icon {
      font-size: 1.25rem;
  }
  
  .loginPage-title {
      font-size: 1.12rem;
  }
  
  .loginPage-input {
    padding: 0.75rem 1rem;
    font-size: 0.90rem;
  }
  
  .loginPage-submit-btn, .loginPage-button {
    padding: 0.625rem;
    font-size: 0.85rem;
  }
}
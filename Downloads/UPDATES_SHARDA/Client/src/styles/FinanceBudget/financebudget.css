/* Dark Mode Styles */
[data-theme="dark"] .financebudget-container {
    background-color: #1a1f2c;
    color: #e0e3e9;
    box-shadow: 0 0 1.43rem rgba(0, 0, 0, 0.3);
  }
  
  [data-theme="dark"] .financebudget-header {
    border-bottom-color: #2d3441;
  }
  
  [data-theme="dark"] .financebudget-title {
    color: #f5f7fa;
  }
  
  [data-theme="dark"] .financebudget-back-btn {
    background-color: #2d3441;
    border-color: #3a4252;
    color: #e0e3e9;
  }
  
  [data-theme="dark"] .financebudget-back-btn:hover {
    background-color: #3a4252;
  }
  
  [data-theme="dark"] .financebudget-message.success {
    background-color: #1f3a1f;
    color: #81c784;
    border-color: #66bb6a;
  }
  
  [data-theme="dark"] .financebudget-message.error {
    background-color: #3a1f1f;
    color: #ff7d7d;
    border-color: #ff5252;
  }
  
  [data-theme="dark"] .financebudget-form-section {
    background-color: #232a38;
    box-shadow: 0 2.0px 5px rgba(0, 0, 0, 0.2);
  }
  
  [data-theme="dark"] .financebudget-form-section-title {
    color: #d0d4dc;
    border-bottom-color: #3a4252;
  }
  
  [data-theme="dark"] .financebudget-form-label {
    color: #d0d4dc;
  }
  
  [data-theme="dark"] .financebudget-form-input,
  [data-theme="dark"] .financebudget-form-select {
    background-color: #2d3441;
    border-color: #3a4252;
    color: #e0e3e9;
  }
  
  [data-theme="dark"] .financebudget-form-input:focus,
  [data-theme="dark"] .financebudget-form-select:focus {
    border-color: #4d7cff;
    box-shadow: 0 0 0 0.2rem rgba(77, 124, 255, 0.3);
  }
  
  [data-theme="dark"] .financebudget-form-select {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23a0a4ab' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  }
  
  [data-theme="dark"] .financebudget-form-submit-btn {
    background-color: #2e7d32;
  }
  
  [data-theme="dark"] .financebudget-form-submit-btn:hover {
    background-color: #3a8a40;
  }
  
  [data-theme="dark"] .financebudget-form-submit-btn:disabled {
    background-color: #4b5563;
  }

/* Container Styles */
.financebudget-container {
    max-width: 64.3rem;
    margin: 1.60rem auto;
    padding: 1.60rem;
    background: #fff;
    border-radius: 0.71rem;
    box-shadow: 0 0 1.43rem rgba(0, 0, 0, 0.1);
}

/* Header Styles */
.financebudget-header {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1.0px solid #eee;
}

.financebudget-title {
    font-size: 1.20rem;
    color: #2c3e50;
    margin-left: 1rem;
    flex-grow: 1;
}

/* Back Button */
.financebudget-back-btn {
    background: #f8f9fa;
    border: 1.0px solid #ddd;
    color: #333;
    padding: 0.5rem 1rem;
    border-radius: 0.357rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.financebudget-back-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

/* Message Styles */
.financebudget-message {
    padding: 1rem;
    margin-bottom: 1.5rem;
    border-radius: 0.357rem;
    font-weight: 500;
}

.financebudget-message.success {
    background: #d4edda;
    color: #155724;
    border: 1.0px solid #c3e6cb;
}

.financebudget-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1.0px solid #f5c6cb;
}

/* Form Styles */
.financebudget-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.financebudget-form-section {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.57rem;
    box-shadow: 0 2.0px 5px rgba(0, 0, 0, 0.05);
}

.financebudget-form-section-title {
    font-size: 0.90rem;
    color: #495057;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1.0px solid #dee2e6;
}

.financebudget-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(17.9rem, 1fr));
    gap: 1.5rem;
}

.financebudget-form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 8rem;
}

.financebudget-form-label {
    font-weight: 500;
    color: #495057;
    font-size: 0.75rem;
}

.financebudget-form-input,
.financebudget-form-select {
    padding: 0.75rem 1rem;
    border: 1.0px solid #ced4da;
    border-radius: 0.357rem;
    font-size: 0.75rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.financebudget-form-input:focus,
.financebudget-form-select:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.financebudget-form-select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1em;
}

/* Form Actions */
.financebudget-form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 1rem;
}

.financebudget-form-submit-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.357rem;
    font-size: 0.75rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background-color 0.3s ease;
}

.financebudget-form-submit-btn:hover {
    background: #218838;
}

.financebudget-form-submit-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}
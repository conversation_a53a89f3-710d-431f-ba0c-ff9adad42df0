/* Dark Mode Styles */
[data-theme="dark"] .supervisor-container {
  background-color: #1a1f2c;
  color: #e0e3e9;
}

[data-theme="dark"] .supervisor-back-button {
  color: #4d7cff;
}

[data-theme="dark"] .supervisor-back-button:hover {
  background-color: rgba(77, 124, 255, 0.1);
  color: #3a6bd9;
}

[data-theme="dark"] .supervisor-form-card {
  background-color: #232a38;
  border-color: #2d3441;
  box-shadow: 0 0.71rem 2.1rem rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .supervisor-form-title {
  color: #f5f7fa;
  border-bottom-color: #3a4252;
}

[data-theme="dark"] .supervisor-form-title::after {
  background-color: #4d7cff;
}

[data-theme="dark"] .supervisor-loading {
  background-color: #2d3441;
  color: #a0a4ab;
  box-shadow: 0 2.0px 5px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .supervisor-message-success {
  background-color: rgba(39, 78, 19, 0.5);
  color: #a5d6a7;
  border-left-color: #66bb6a;
}

[data-theme="dark"] .supervisor-message-error {
  background-color: rgba(78, 19, 19, 0.5);
  color: #ff8a80;
  border-left-color: #ff5252;
}

[data-theme="dark"] .supervisor-label {
  color: #d0d4dc;
}

[data-theme="dark"] .supervisor-input,
[data-theme="dark"] .supervisor-select,
[data-theme="dark"] .supervisor-search-input {
  background-color: #2d3441;
  border-color: #3a4252;
  color: #e0e3e9;
}

[data-theme="dark"] .supervisor-input:hover,
[data-theme="dark"] .supervisor-select:hover,
[data-theme="dark"] .supervisor-search-input:hover {
  border-color: #4b5563;
}

[data-theme="dark"] .supervisor-input:focus,
[data-theme="dark"] .supervisor-select:focus,
[data-theme="dark"] .supervisor-search-input:focus {
  border-color: #4d7cff;
  box-shadow: 0 0 0 0.214rem rgba(77, 124, 255, 0.3);
  background-color: #232a38;
}

[data-theme="dark"] .supervisor-committee-checkboxes {
  background-color: #2d3441;
  border-color: #3a4252;
  box-shadow: inset 0 2.0px 4px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .supervisor-committee-checkboxes::-webkit-scrollbar-track {
  background: #2d3441;
}

[data-theme="dark"] .supervisor-committee-checkboxes::-webkit-scrollbar-thumb {
  background: #4b5563;
}

[data-theme="dark"] .supervisor-committee-checkboxes::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

[data-theme="dark"] .supervisor-checkbox-item:hover {
  background-color: rgba(77, 124, 255, 0.1);
}

[data-theme="dark"] .supervisor-checkbox {
  accent-color: #4d7cff;
}

[data-theme="dark"] .supervisor-checkbox-label {
  color: #e0e3e9;
}

[data-theme="dark"] .supervisor-checkbox-label:hover {
  color: #4d7cff;
}

[data-theme="dark"] .supervisor-search-container::before {
  color: #6b7280;
}

[data-theme="dark"] .supervisor-no-results {
  color: #a0a4ab;
  background-color: #2d3441;
  border-color: #3a4252;
}

[data-theme="dark"] .supervisor-submit-button {
  background-color: #2e7d32;
  box-shadow: 0 0.286rem 0.429rem rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .supervisor-submit-button:hover {
  background-color: #3a8a40;
  box-shadow: 0 0.429rem 0.86rem rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .supervisor-submit-button:disabled {
  background-color: #4b5563;
}
/* Main Container */
.supervisor-container {
  max-width: 64.3rem;
  margin: 0 auto;
  padding: 1.60rem;
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #2c3e50;
  background-color: #f8fafc;
  min-height: 100vh;
}

/* Back Button */
.supervisor-back-button {
  background: none;
  border: none;
  color: #3498db;
  font-size: 0.75rem;
  cursor: pointer;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 0.429rem;
}

.supervisor-back-button:hover {
  color: #2980b9;
  background-color: rgba(52, 152, 219, 0.1);
  transform: translateX(-0.214rem);
}

/* Form Card */
.supervisor-form-card {
  background: white;
  border-radius: 1.14rem;
  box-shadow: 0 0.71rem 2.1rem rgba(0, 0, 0, 0.08);
  padding: 2.00rem;
  transition: all 0.3s ease;
  border: 1.0px solid rgba(0, 0, 0, 0.05);
}

.supervisor-form-card:hover {
  box-shadow: 0 0.86rem 2.9rem rgba(0, 0, 0, 0.12);
}

/* Form Title */
.supervisor-form-title {
  color: #2c3e50;
  margin-bottom: 1.8rem;
  font-size: 1.20rem;
  font-weight: 700;
  border-bottom: 2.0px solid #f1f2f6;
  padding-bottom: 1rem;
  position: relative;
}

.supervisor-form-title::after {
  content: '';
  position: absolute;
  bottom: -2.0px;
  left: 0;
  width: 4.3rem;
  height: 2.0px;
  background-color: #3498db;
}

/* Loading Indicator */
.supervisor-loading {
  padding: 1.2rem;
  background: #f8f9fa;
  border-radius: 0.86rem;
  margin-bottom: 1.5rem;
  text-align: center;
  color: #7f8c8d;
  box-shadow: 0 2.0px 5px rgba(0, 0, 0, 0.05);
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

/* Messages */
.supervisor-message {
  padding: 1.2rem;
  border-radius: 0.86rem;
  margin-bottom: 1.8rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  position: relative;
  transition: all 0.3s ease;
}

.supervisor-message-success {
  background-color: rgba(212, 237, 218, 0.5);
  color: #155724;
  border-left: 0.286rem solid #28a745;
}

.supervisor-message-error {
  background-color: rgba(248, 215, 218, 0.5);
  color: #721c24;
  border-left: 0.286rem solid #dc3545;
}

/* Form Grid */
.supervisor-form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(21.4rem, 1fr));
  gap: 1.8rem;
  margin-bottom: 2rem;
}

/* Form Groups */
.supervisor-form-group {
  margin-bottom: 1.5rem;
  transition: all 0.2s ease;
}

.supervisor-form-group:hover {
  transform: translateY(-2.0px);
}

.supervisor-label {
  display: block;
  margin-bottom: 0.8rem;
  font-weight: 600;
  color: #34495e;
  font-size: 0.80rem;
  letter-spacing: 0.02em;
}

.supervisor-input,
.supervisor-select {
  width: 100%;
  padding: 0.9rem 1.1rem;
  border: 1.0px solid #dfe6e9;
  border-radius: 0.71rem;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  background-color: #f8fafc;
  color: #2c3e50;
}

.supervisor-input:hover,
.supervisor-select:hover {
  border-color: #a0b0c5;
}

.supervisor-input:focus,
.supervisor-select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 0.214rem rgba(52, 152, 219, 0.2);
  background-color: white;
}

/* Committee Section */
.supervisor-committee-group {
  grid-column: span 2;
}

.supervisor-committee-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(17.9rem, 1fr));
  gap: 1rem;
  background: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.86rem;
  border: 1.0px solid #e5e7eb;
  max-height: 10.7rem;
  overflow: auto;
  scrollbar-width: thin;
  scrollbar-color: #bdc3c7 #f1f1f1;
  box-shadow: inset 0 2.0px 4px rgba(0, 0, 0, 0.03);
}

/* Scrollbar styling */
.supervisor-committee-checkboxes::-webkit-scrollbar {
  width: 0.429rem;
}

.supervisor-committee-checkboxes::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 0.71rem;
}

.supervisor-committee-checkboxes::-webkit-scrollbar-thumb {
  background: #bdc3c7;
  border-radius: 0.71rem;
  transition: background 0.3s ease;
}

.supervisor-committee-checkboxes::-webkit-scrollbar-thumb:hover {
  background: #95a5a6;
}

/* Ensure checkbox items don't overflow */
.supervisor-checkbox-item {
  min-width: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.5rem;
  border-radius: 0.57rem;
  transition: all 0.2s ease;
}

.supervisor-checkbox-item:hover {
  background-color: rgba(52, 152, 219, 0.1);
}

.supervisor-checkbox {
  width: 1.2rem;
  height: 1.2rem;
  accent-color: #3498db;
  cursor: pointer;
  transition: all 0.2s ease;
}

.supervisor-checkbox:hover {
  transform: scale(1.1);
}

.supervisor-checkbox-label {
  font-size: 0.80rem;
  color: #2d3436;
  cursor: pointer;
  transition: color 0.2s ease;
}

.supervisor-checkbox-label:hover {
  color: #3498db;
}

/* Faculty Search */
.supervisor-search-container {
  margin-bottom: 1.2rem;
  position: relative;
}

.supervisor-search-container::before {
  content: '🔍';
  position: absolute;
  left: 0.86rem;
  top: 50%;
  transform: translateY(-50%);
  color: #a0aec0;
  font-size: 0.75rem;
  pointer-events: none;
}

.supervisor-search-input {
  width: 100%;
  max-width: 21.4rem;
  padding: 0.9rem 1.1rem 0.9rem 2.5rem;
  border: 1.0px solid #dfe6e9;
  border-radius: 0.71rem;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  background-color: #f8fafc;
}

.supervisor-search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 0.214rem rgba(52, 152, 219, 0.2);
  background-color: white;
  width: 100%;
}

/* No results message */
.supervisor-no-results {
  padding: 1.2rem;
  text-align: center;
  color: #7f8c8d;
  font-style: italic;
  background: #f9fafb;
  border-radius: 0.71rem;
  border: 1.0px dashed #dfe6e9;
}

/* Submit Button */
.supervisor-submit-button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.71rem;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  max-width: 16.1rem;
  margin: 1.5rem auto 0;
  display: block;
  box-shadow: 0 0.286rem 0.429rem rgba(52, 152, 219, 0.2);
  position: relative;
  overflow: hidden;
}

.supervisor-submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transition: all 0.6s ease;
}

.supervisor-submit-button:hover {
  background-color: #2980b9;
  transform: translateY(-0.214rem);
  box-shadow: 0 0.429rem 0.86rem rgba(52, 152, 219, 0.3);
}

.supervisor-submit-button:hover::before {
  left: 100%;
}

.supervisor-submit-button:active {
  transform: translateY(-1.0px);
  box-shadow: 0 0.214rem 0.57rem rgba(52, 152, 219, 0.2);
}

.supervisor-submit-button:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.supervisor-submit-button:disabled::before {
  display: none;
}

/* Responsive Adjustments */
@media (max-width: 53.1rem) {
  .supervisor-container {
    padding: 1.5rem;
  }
  
  .supervisor-form-card {
    padding: 1.60rem;
  }
}

@media (max-width: 41.1rem) {
  .supervisor-form-grid {
    grid-template-columns: 1fr;
    gap: 1.2rem;
  }
  
  .supervisor-committee-group {
    grid-column: span 1;
  }
  
  .supervisor-form-card {
    padding: 1.5rem;
    border-radius: 0.86rem;
  }
  
  .supervisor-form-title {
    font-size: 1.00rem;
  }
  
  .supervisor-search-input {
    width: 100%;
  }
}

@media (max-width: 25.7rem) {
  .supervisor-container {
    padding: 1rem;
  }
  
  .supervisor-form-card {
    padding: 1.2rem;
    border-radius: 0.71rem;
  }
  
  .supervisor-committee-checkboxes {
    grid-template-columns: 1fr;
  }
  
  .supervisor-label {
    font-size: 0.75rem;
  }
  
  .supervisor-input,
  .supervisor-select,
  .supervisor-search-input {
    padding: 0.8rem 1rem;
    font-size: 0.80rem;
  }
  
  .supervisor-submit-button {
    padding: 0.9rem 1.5rem;
  }
}

/* Unsupervise Button and Dialog Styles */
.supervisor-form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-start;
  align-items: center;
  margin-top: 1rem;
}

.supervisor-unsupervise-button {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.supervisor-unsupervise-button:hover:not(:disabled) {
  background-color: #c82333;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.supervisor-unsupervise-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Unsupervise Dialog Styles */
.supervisor-unsupervise-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.supervisor-unsupervise-dialog {
  background-color: white;
  padding: 2rem;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 90%;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.supervisor-unsupervise-dialog-icon {
  text-align: center;
  font-size: 3rem;
  color: #f39c12;
  margin-bottom: 1rem;
}

.supervisor-unsupervise-dialog h3 {
  text-align: center;
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.5rem;
}

.supervisor-unsupervise-dialog p {
  text-align: center;
  margin-bottom: 0.5rem;
  color: #666;
  line-height: 1.5;
}

.supervisor-unsupervise-dialog-warning {
  font-weight: bold;
  color: #dc3545;
  margin-top: 1rem;
}

.supervisor-unsupervise-dialog-list {
  list-style-type: disc;
  margin-left: 2rem;
  margin-bottom: 1.5rem;
  color: #666;
}

.supervisor-unsupervise-dialog-list li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.supervisor-unsupervise-dialog-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.supervisor-unsupervise-dialog-cancel,
.supervisor-unsupervise-dialog-confirm {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.supervisor-unsupervise-dialog-cancel {
  background-color: #6c757d;
  color: white;
}

.supervisor-unsupervise-dialog-cancel:hover:not(:disabled) {
  background-color: #5a6268;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

.supervisor-unsupervise-dialog-confirm {
  background-color: #dc3545;
  color: white;
}

.supervisor-unsupervise-dialog-confirm:hover:not(:disabled) {
  background-color: #c82333;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.supervisor-unsupervise-dialog-cancel:disabled,
.supervisor-unsupervise-dialog-confirm:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Dark theme styles for unsupervise dialog */
[data-theme="dark"] .supervisor-unsupervise-dialog {
  background-color: #232a38;
  color: #e0e3e9;
}

[data-theme="dark"] .supervisor-unsupervise-dialog h3 {
  color: #f5f7fa;
}

[data-theme="dark"] .supervisor-unsupervise-dialog p {
  color: #a0a4ab;
}

[data-theme="dark"] .supervisor-unsupervise-dialog-list {
  color: #a0a4ab;
}

[data-theme="dark"] .supervisor-unsupervise-dialog-warning {
  color: #ff5252;
}

/* Responsive styles for unsupervise button */
@media (max-width: 48rem) {
  .supervisor-form-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .supervisor-unsupervise-button {
    width: 100%;
    justify-content: center;
  }
  
  .supervisor-unsupervise-dialog {
    padding: 1.5rem;
  }
  
  .supervisor-unsupervise-dialog-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .supervisor-unsupervise-dialog-cancel,
  .supervisor-unsupervise-dialog-confirm {
    width: 100%;
  }
}

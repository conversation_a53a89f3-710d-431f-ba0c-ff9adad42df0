/* Dark Theme Styles */
[data-theme="dark"] .reset-password-container {
    background: linear-gradient(135deg, #1a1f2c 0%, #0d111a 100%);
  }
  
  [data-theme="dark"] .reset-password-card {
    background-color: #2a3142;
    box-shadow: 0 0.71rem 2.1rem rgba(0, 0, 0, 0.3);
  }
  
  [data-theme="dark"] .reset-password-header .header-icon {
    color: #4d9fec;
  }
  
  [data-theme="dark"] .reset-password-header h2 {
    color: #e0e3e7;
  }
  
  [data-theme="dark"] .reset-password-header p {
    color: #a1a8b5;
  }
  
  [data-theme="dark"] .input-group label {
    color: #e0e3e7;
  }
  
  [data-theme="dark"] .password-input-wrapper input {
    background-color: #1a1f2c;
    border-color: #3a4255;
    color: #e0e3e7;
  }
  
  [data-theme="dark"] .password-input-wrapper input.has-value {
    border-color: #4d9fec;
  }
  
  [data-theme="dark"] .password-input-wrapper input:focus {
    border-color: #4d9fec;
    box-shadow: 0 0 0 0.214rem rgba(77, 159, 236, 0.3);
  }
  
  [data-theme="dark"] .toggle-password {
    color: #7f8c8d;
  }
  
  [data-theme="dark"] .toggle-password:hover {
    color: #4d9fec;
  }
  
  [data-theme="dark"] .strength-bar:not(.active) {
    background-color: #3a4255 !important;
  }
  
  [data-theme="dark"] .strength-text {
    color: #a1a8b5;
  }
  
  [data-theme="dark"] .reset-button {
    background-color: #4d9fec;
    color: #1a1f2c;
  }
  
  [data-theme="dark"] .reset-button:disabled {
    background-color: #7f8c8d;
    color: #2a3142;
  }
  
  [data-theme="dark"] .reset-button:not(:disabled):hover {
    background-color: #3a8fd9;
  }
  
  [data-theme="dark"] .message.success {
    background-color: rgba(39, 174, 96, 0.1);
    color: #2ecc71;
    border-color: rgba(39, 174, 96, 0.3);
  }
  
  [data-theme="dark"] .message.error {
    background-color: rgba(255, 107, 107, 0.1);
    color: #ff6b6b;
    border-color: rgba(255, 107, 107, 0.3);
  }
  
  [data-theme="dark"] .password-tips {
    border-top-color: #3a4255;
  }
  
  [data-theme="dark"] .password-tips h4 {
    color: #e0e3e7;
  }
  
  [data-theme="dark"] .password-tips li {
    color: #a1a8b5;
  }
  
  [data-theme="dark"] .password-tips li:before {
    color: #7f8c8d;
  }
  
  [data-theme="dark"] .password-tips li.met {
    color: #2ecc71;
  }
  
  [data-theme="dark"] .password-tips li.met:before {
    color: #2ecc71;
  }
  
  /* Placeholder text color */
  [data-theme="dark"] .password-input-wrapper input::placeholder {
    color: #7f8c8d;
  }
  
  /* Animation for dark theme */
  @keyframes resetPasswordFadeInDark {
    from {
      opacity: 0;
      background-color: transparent;
    }
    to {
      opacity: 1;
      background-color: #2a3142;
    }
  }
  
  [data-theme="dark"] .reset-password-card {
    animation: resetPasswordFadeInDark 0.3s ease-out forwards;
  }
  
  /* Responsive adjustments for dark theme */
  @media (max-width: 30.9rem) {
    [data-theme="dark"] .reset-password-card {
      background-color: #2a3142;
    }
  }

.reset-password-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    min-width: 40vw;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 1.21rem;
}

.reset-password-card {
    background: white;
    border-radius: 0.86rem;
    box-shadow: 0 0.71rem 2.1rem rgba(0, 0, 0, 0.1);
    padding: 1.68rem;
    /* width: 100%; */
    max-width: 26.8rem;
}

.reset-password-header {
    text-align: center;
    margin-bottom: 1.82rem;
}

.reset-password-header .header-icon {
    font-size: 1.50rem;
    color: #4a6baf;
    margin-bottom: 1.07rem;
}

.reset-password-header h2 {
    color: #2c3e50;
    margin-bottom: 0.71rem;
    font-size: 1.20rem;
}

.reset-password-header p {
    color: #7f8c8d;
    font-size: 0.75rem;
}

.reset-password-form {
    display: flex;
    flex-direction: column;
    gap: 1.52rem;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 0.57rem;
}

.input-group label {
    font-size: 0.75rem;
    color: #555;
    font-weight: 500;
}

.password-input-wrapper {
    position: relative;
}

.password-input-wrapper input {
    width: 100%;
    padding: 1.00rem 1.14rem;
    border: 1.0px solid #ddd;
    border-radius: 0.57rem;
    font-size: 0.75rem;
    transition: all 0.3s ease;
}

.password-input-wrapper input.has-value {
    border-color: #4a6baf;
}

.password-input-wrapper input:focus {
    outline: none;
    border-color: #4a6baf;
    box-shadow: 0 0 0 0.214rem rgba(74, 107, 175, 0.2);
}

.toggle-password {
    position: absolute;
    right: 1.07rem;
    top: 50%;
    transform: translateY(-50%);
    color: #95a5a6;
    cursor: pointer;
    transition: color 0.2s ease;
}

.toggle-password:hover {
    color: #4a6baf;
}

.password-strength {
    margin-top: 0.71rem;
}

.strength-meter {
    display: flex;
    gap: 0.357rem;
    margin-bottom: 0.357rem;
}

.strength-bar {
    height: 0.286rem;
    flex: 1;
    border-radius: 2.0px;
    transition: background-color 0.3s ease;
}

.strength-text {
    font-size: 0.75rem;
    color: #95a5a6;
    text-align: right;
}

.reset-button {
    background-color: #4a6baf;
    color: white;
    border: none;
    padding: 1.00rem;
    border-radius: 0.57rem;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.57rem;
}

.reset-button:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
}

.reset-button:not(:disabled):hover {
    background-color: #3a5a9f;
    transform: translateY(-2.0px);
    box-shadow: 0 0.286rem 0.71rem rgba(74, 107, 175, 0.3);
}

.message {
    padding: 0.86rem 1.14rem;
    border-radius: 0.57rem;
    margin-top: 1.21rem;
    font-size: 0.75rem;
    text-align: center;
}

.message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1.0px solid #c3e6cb;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1.0px solid #f5c6cb;
}

.password-tips {
    margin-top: 1.82rem;
    padding-top: 1.21rem;
    border-top: 1.0px solid #eee;
}

.password-tips h4 {
    color: #2c3e50;
    margin-bottom: 0.71rem;
    font-size: 0.75rem;
}

.password-tips ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 0.71rem;
}

.password-tips li {
    font-size: 0.75rem;
    color: #7f8c8d;
    position: relative;
    padding-left: 1.21rem;
    flex: 1 1 45%;
}

.password-tips li:before {
    content: "•";
    position: absolute;
    left: 0;
    color: #95a5a6;
}

.password-tips li.met {
    color: #4CAF50;
}

.password-tips li.met:before {
    content: "✓";
    color: #4CAF50;
}

@media (max-width: 30.9rem) {
    .reset-password-card {
        padding: 1.82rem 1.21rem;
    }
    
    .password-tips li {
        flex: 1 1 100%;
    }
}
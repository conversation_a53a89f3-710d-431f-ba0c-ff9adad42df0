/* Dark Theme Styles */
[data-theme="dark"] .leaves-container {
  background-color: #1a1f2c;
  box-shadow: 0 0.286rem 0.86rem rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .leaves-title {
  color: #e0e3e7;
}

[data-theme="dark"] .st-leaves-header {
  border-bottom-color: #3a4255;
}

[data-theme="dark"] .manage-leave-btn {
  background-color: #27ae60;
  color: #e0e3e7;
}

[data-theme="dark"] .manage-leave-btn:hover {
  background-color: #2ecc71;
  box-shadow: 0 0.286rem 0.57rem rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .leaves-table {
  color: #e0e3e7;
}

[data-theme="dark"] .leaves-table th {
  background-color: #4d9fec;
  color: #1a1f2c;
  border-color: #3a4255;
}

[data-theme="dark"] .leaves-table td {
  border-color: #3a4255;
  color: #e0e3e7;
}

[data-theme="dark"] .leaves-table tbody tr:nth-child(even) {
  background-color: #2a3142;
}

[data-theme="dark"] .leaves-table tbody tr:hover {
  background-color: #3a4255;
}

[data-theme="dark"] .loading-message,
[data-theme="dark"] .leaves-message {
  color: #a1a8b5;
}

/* Dark theme icons */
[data-theme="dark"] .faChalkboardTeacher {
  color: #4d9fec;
}

/* Animation for dark theme */
@keyframes leavesFadeInDark {
  from {
    opacity: 0;
    background-color: transparent;
  }
  to {
    opacity: 1;
    background-color: #1a1f2c;
  }
}

[data-theme="dark"] .leaves-container {
  animation: leavesFadeInDark 0.3s ease-out forwards;
}

/* Status specific colors in dark theme */
[data-theme="dark"] .student-leave {
  font-weight: 500;
}

[data-theme="dark"] .student-leave[data-status="approved"] {
  color: #2ecc71;
}

[data-theme="dark"] .student-leave[data-status="pending"] {
  color: #f39c12;
}

[data-theme="dark"] .student-leave[data-status="rejected"] {
  color: #e74c3c;
}
/* Container */
.leaves-container {
  width: 100%;
  max-width: 64.3rem;
  margin: 1.82rem auto;
  padding: 1.21rem;
  background: #ffffff;
  border-radius: 0.71rem;
  box-shadow: 0 0.286rem 0.86rem rgba(0, 0, 0, 0.1);
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

/* Title */
.leaves-title {
  font-size: 1.03rem;
  color: #333;
  font-weight: bold;
}

/* Table Styling */
.leaves-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 1.00rem;
}

.leaves-table th,
.leaves-table td {
  border: 1.0px solid #ddd;
  padding: 0.86rem;
  text-align: left;
}

.leaves-table th {
  background-color: #007bff;
  color: #ffffff;
  text-transform: uppercase;
}

.leaves-table tbody tr:nth-child(even) {
  background-color: #f9f9f9;
}

/* Table Row Hover Effect */
.leaves-table tbody tr:hover {
  background-color: #f1f1f1;
}

/* Responsive Design */
@media (max-width: 41.1rem) {
  .leaves-container {
    width: 95%;
    padding: 1.07rem;
  }

  .leaves-table th,
  .leaves-table td {
    font-size: 1.00rem;
    padding: 0.71rem;
  }
}

.st-leaves-header {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin-bottom: 1.52rem;
  padding: 0 1.0px;
  border-bottom: 2.0px solid #dee2e6;
  /* border: 0.214rem solid red; */

}

/* Manage Button */
.manage-leave-btn {
  background-color: #2ecc71;
  /* Green */
  color: white;
  padding: 0.71rem 1.21rem;
  border: none;
  border-radius: 0.57rem;
  cursor: pointer;
  font-size: 0.75rem;
  transition: background-color 0.3s ease;
  box-shadow: 0 2.0px 5px rgba(0, 0, 0, 0.15);
  display: inline-flex;
  align-items: center;
  gap: 0.57rem;
}


.manage-leave-btn:hover {
  background-color: #27ae60;
  box-shadow: 0 0.286rem 0.57rem rgba(0, 0, 0, 0.2);
}
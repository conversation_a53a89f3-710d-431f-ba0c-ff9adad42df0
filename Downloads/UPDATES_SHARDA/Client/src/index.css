

:root {
  /* Base font size for responsive scaling - 14px base */
  font-size: 1.00rem;
  
  /* Responsive spacing variables */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Responsive font sizes */
  --font-xs: 0.150rem;
  --font-sm: 0.175rem;
  --font-base: 0.200rem;
  --font-lg: 0.225rem;
  --font-xl: 0.250rem;
  --font-2xl: 0.300rem;
  --font-3xl: 0.375rem;
  --font-4xl: 0.450rem;
  
  /* Container widths */
  --container-sm: 100%;
  --container-md: 48rem;
  --container-lg: 64rem;
  --container-xl: 80rem;
  
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.00.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 17.1rem;
  min-height: 100vh;
}

h1 {
  font-size: var(--font-2xl);
  line-height: 1.00.1;
}

button {
  border-radius: 0.5rem;
  border: 1.0px solid transparent;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-sm);
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 0.286rem auto -webkit-focus-ring-color;
}

/* Responsive breakpoints */
@media (max-width: 32.9rem) {
  :root {
    font-size: 0.88rem; /* Smaller base font on mobile */
    --spacing-md: 0.75rem;
    --spacing-lg: 1rem;
    --spacing-xl: 1.25rem;
  }
  
  body {
    min-width: 100%;
    padding: var(--spacing-sm);
  }
  
  button {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-xs);
  }
}

@media (max-width: 20.6rem) {
  :root {
    font-size: 0.80rem; /* Even smaller on very small screens */
    --spacing-sm: 0.375rem;
    --spacing-md: 0.5rem;
    --spacing-lg: 0.75rem;
  }
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

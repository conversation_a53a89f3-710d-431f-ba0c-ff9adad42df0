{"name": "backend", "version": "1.0.0", "description": "<PERSON><PERSON><PERSON> backend", "type": "module", "main": "index.js", "scripts": {"dev": "nodemon -r dotenv/config --experimental-json-modules src/index.js"}, "keywords": ["backend"], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"nodemon": "^3.1.9"}, "dependencies": {"bcrypt": "^5.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongodb": "^6.12.0", "mongoose": "^8.9.5", "nodemailer": "^6.10.0", "socket": "^0.14.77", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "uuid": "^11.0.5", "winston": "^3.17.0"}}